@use 'components/shared/styles/mixins' as *;

#modal_root {
    .load-strategy {
        &__recent {
            display: flex;
            gap: 1.6rem;

            &__files {
                width: 35%;
                overflow: auto;
                height: 100%;
                margin: 0.2rem 0;
            }

            &__empty {
                @include flex-center;

                align-items: center;
                flex-direction: column;

                &-icon {
                    margin-bottom: 1.6rem;
                }

                &-title {
                    margin-bottom: 0.8rem;
                    font-size: var(--text-size-s);
                    font-weight: bold;
                    line-height: 2.4rem;
                }

                &-description {
                    margin-bottom: 1.6rem;
                    font-size: var(--text-size-xs);
                    line-height: 2rem;
                }

                &-expand {
                    margin-bottom: 0.8rem;
                    color: var(--brand-red-coral);
                    font-size: var(--text-size-xs);
                    font-weight: bold;
                    cursor: pointer;

                    &:hover {
                        text-decoration: underline;
                    }
                }

                &-explanation {
                    font-size: var(--text-size-xxs);
                    text-align: left;
                    opacity: 0;

                    &-list {
                        margin-top: 0.8rem;
                    }

                    &--show {
                        opacity: 1;
                        width: fit-content;
                    }
                }
            }

            &-item {
                grid-template-columns: 1fr 0.6fr;
                position: relative;
                display: grid;
                grid-template-areas: ('text location');
                padding: 1rem 0.8rem;
                align-items: center;
                text-align: center;

                &:hover {
                    cursor: pointer;
                }

                &:not(:last-child) {
                    border-bottom: solid 1px var(--border-divider);
                }

                &--selected {
                    background-color: var(--general-section-2);
                }

                &-text {
                    height: unset;
                    flex-direction: column;
                    text-align: start;
                    padding-right: 0.8rem;
                }

                &-title {
                    font-size: var(--text-size-xs);
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                &-time {
                    font-size: var(--text-size-xxs);
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                &-saved {
                    margin-inline-start: 1rem;
                    font-size: var(--text-size-xs);
                    line-height: 1.43;
                    word-break: break-word;
                }

                &-location {
                    @include flex-center(flex-start);

                    width: 100%;
                    word-break: break-word;
                    color: var(--text-general);
                    height: 100%;
                }
            }

            &__preview {
                width: 65%;
                flex-basis: 65%;
                display: flex;
                flex-direction: column;

                .load-strategy__preview-workspace {
                    height: calc(100% - 5.2rem);
                    min-height: unset;
                    margin: 0;
                }

                &-title {
                    margin: 1.5rem 0;
                    margin-left: 0;
                }

                &__title {
                    margin-left: 0;
                }
            }
        }

        &__container {
            &--has-footer {
                height: calc(80vh - 21rem);
                margin-top: -1rem;
            }
        }

        &__title {
            margin: 1.5rem;
        }
    }

    .load-strategy__preview-workspace {
        min-height: unset;
        height: unset;
        margin: 0;
    }
}
