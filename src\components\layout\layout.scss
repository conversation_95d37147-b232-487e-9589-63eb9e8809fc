@use 'components/shared/styles/mixins' as *;

.layout {
    display: flex;
    flex-direction: column;
    height: 100vh;
    position: relative; // For positioning global RunPanel

    .deriv-header {
        background: var(--general-main-1);
        height: 4.9rem;

        @include mobile-or-tablet-screen {
            height: 4rem;
        }
    }

    // Global RunPanel positioning - Available on all pages
    .run-panel {
        position: fixed !important;
        top: 60px !important; // Below header
        right: 0 !important;
        z-index: 1000 !important; // Above other content

        @include mobile-or-tablet-screen {
            top: 50px !important; // Adjust for mobile header
        }
    }
}
