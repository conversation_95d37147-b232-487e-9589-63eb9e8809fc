// Modern frosted mobile menu with professional styling
.mobile-menu {
    height: 100%;

    &__toggle {
        padding-inline-start: 1rem;
        padding-inline-end: 1rem;
        border-inline-end: 1px solid var(--nav-border, var(--general-section-1));
        display: flex;
        height: 100%;
        cursor: pointer;
        transition: all 180ms ease;
        border-radius: var(--nav-radius, 10px);

        &:hover {
            background: var(--nav-hover-bg, rgba(255, 255, 255, 0.05));
            transform: translateY(-1px);
        }

        &:active {
            transform: translateY(0);
            background: rgba(255, 255, 255, 0.08);
        }
    }

    &__footer {
        justify-content: center;
        height: 4rem;
    }

    &__header {
        display: flex;
        padding-inline-end: 1.6rem;
        padding-inline-start: 0.4rem;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        &__language {
            display: flex;
            align-items: center;
            gap: 0.4rem;
        }
    }

    &__content {
        display: flex;
        flex-direction: column;
        height: 100%;

        &__platform {
            display: flex;
            justify-content: center;
            align-items: center;
            border-bottom: 1px solid var(--general-section-1);
            min-height: 7rem;
            cursor: pointer;
        }

        .deriv-context-menu {
            box-shadow: none !important;
        }

        &__items {
            position: relative;
            height: 100%;
            padding-top: 1rem;

            &--padding {
                padding-inline-start: 4.8rem;
                padding-inline-end: 1.6rem;
            }

            &--bottom-border {
                border-bottom: 1px solid var(--general-section-1);
            }

            &__item {
                height: 5.6rem;
                cursor: pointer;
                width: 100%;
                transition: all 180ms ease;
                border-radius: var(--nav-radius, 10px);
                margin: 2px 8px;
                padding: 0 12px;
                display: flex;
                align-items: center;
                min-height: 48px; // Large touch target

                &:hover {
                    background: var(--nav-hover-bg, rgba(255, 255, 255, 0.05));
                    transform: translateX(4px);
                }

                &:active {
                    background: rgba(255, 255, 255, 0.08);
                    transform: translateX(2px);
                }

                .deriv-toggle-switch {
                    margin-left: auto;
                    margin-right: 1.6rem;
                }

                &--active {
                    background: var(--nav-active-bg, rgba(255, 213, 79, 0.12)) !important;
                    border-left: 3px solid var(--nav-active-underline, #FFD54F);

                    .deriv-text {
                        color: var(--nav-fg-hover, #FFFFFF) !important;
                        font-weight: 600 !important;
                    }
                }
            }

            &--right-margin {
                margin-inline-end: 1.6rem;
            }

            &--chevron {
                position: absolute;
                right: 1.5rem;
            }

            &__icons {
                g > path,
                path {
                    fill: var(--text-prominent);
                }
            }
        }
    }

    &__language-drawer {
        padding-inline-start: 0.8rem;
        padding-inline-end: 0.8rem;

        button {
            height: auto;
        }

        .languages-modal__body-button {
            height: 88px;
            width: 133px;
            display: flex;
            align-items: center;
            flex-direction: column;
            padding: 8px;

            .deriv-text {
                color: var(--text-prominent) !important;
            }

            &-selected {
                border: 1px solid var(--brand-blue, var(--brand-secondary));
                border-radius: 4px;
            }
        }
    }

    &__back-btn {
        display: flex;
        align-items: center;
        padding: 3.2rem;
        padding-top: 2rem;
        cursor: pointer;

        span {
            margin-inline-start: 1.6rem;
        }
    }

    // Frosted glass drawer with modern styling
    .deriv-drawer__container {
        background: rgba(14, 20, 36, 0.95) !important; // Semi-transparent nav background
        backdrop-filter: blur(20px) !important;
        -webkit-backdrop-filter: blur(20px) !important;
        border-left: 1px solid var(--nav-border, rgba(255, 255, 255, 0.06)) !important;
        box-shadow: -8px 0 32px rgba(0, 0, 0, 0.3) !important;
    }

    // Enhanced drawer header
    .deriv-drawer__header {
        background: rgba(255, 255, 255, 0.02) !important;
        border-bottom: 1px solid var(--nav-border, rgba(255, 255, 255, 0.06)) !important;
        backdrop-filter: blur(8px) !important;
    }

    &__toggle,
    &__back-btn,
    .deriv-drawer__header__close-btn {
        button > svg > path,
        svg > path {
            fill: var(--nav-fg, var(--text-prominent));
            transition: fill 180ms ease;
        }

        &:hover {
            svg > path {
                fill: var(--nav-fg-hover, #FFFFFF);
            }
        }
    }
}

// Responsive mobile menu enhancements
@media (max-width: 768px) {
    .mobile-menu {
        .deriv-drawer__container {
            width: 85% !important; // Take more screen space on tablets
            max-width: 400px !important;
        }

        &__content__items__item {
            height: 6rem !important;
            font-size: 15px !important;

            .deriv-text {
                font-size: 15px !important;
            }
        }
    }
}

@media (max-width: 640px) {
    .mobile-menu {
        .deriv-drawer__container {
            width: 90% !important; // Almost full width on mobile
            max-width: none !important;
        }

        &__content__items__item {
            height: 5.6rem !important;
            margin: 1px 4px !important;
            padding: 0 8px !important;

            .deriv-text {
                font-size: 14px !important;
            }
        }

        &__header {
            padding-inline-start: 0.8rem !important;
            padding-inline-end: 0.8rem !important;
        }

        &__back-btn {
            padding: 2rem !important;
            padding-top: 1.5rem !important;
        }
    }
}

@media (max-width: 480px) {
    .mobile-menu {
        .deriv-drawer__container {
            width: 95% !important;
        }

        &__content__items__item {
            height: 5rem !important;

            .deriv-text {
                font-size: 13px !important;
            }
        }
    }
}
