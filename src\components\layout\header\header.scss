@use 'components/shared/styles/constants' as *;
@use 'components/shared/styles/mixins' as *;

// Clean "Pro" dark bar design tokens - Enhanced
:root {
    // Colors - Professional palette
    --nav-bg: #0E1424;
    --nav-bg-alt: #0B101B;
    --nav-border: rgba(255, 255, 255, 0.06);
    --nav-shadow: 0 2px 10px rgba(0, 0, 0, 0.25);

    // Text colors with proper contrast
    --nav-fg: rgba(255, 255, 255, 0.85);        // Default text
    --nav-fg-hover: #FFFFFF;                     // Hover text
    --nav-fg-muted: rgba(255, 255, 255, 0.65);  // Helper text

    // Active states
    --nav-active-bg: rgba(255, 213, 79, 0.12);
    --nav-active-underline: #FFD54F;             // Yellow accent
    --nav-hover-bg: rgba(255, 255, 255, 0.05);

    // CTA styling
    --nav-cta-bg: #FFD54F;
    --nav-cta-fg: #0B101B;
    --nav-cta-hover: #FFC107;
    --nav-cta-glow: rgba(255, 213, 79, 0.2);
    --nav-cta-glow-hover: rgba(255, 213, 79, 0.3);

    // Typography
    --nav-font-size: 14px;
    --nav-font-size-tablet: 13px;
    --nav-font-size-mobile: 13px;
    --nav-font-weight: 500;
    --nav-font-weight-cta: 600;
    --nav-letter-spacing: 0.2px;

    // Layout & spacing
    --nav-radius: 10px;
    --nav-height: 64px;
    --nav-height-tablet: 56px;
    --nav-height-mobile: 52px;
    --nav-item-padding-x: 14px;
    --nav-item-padding-y: 10px;
    --nav-item-gap: 12px;
    --nav-icon-size: 16px;
    --nav-icon-gap: 8px;
}

.app-header {
    // Enhanced professional dark bar styling with modern patterns
    background: var(--nav-bg) !important;
    border-bottom: 1px solid var(--nav-border) !important;
    box-shadow: var(--nav-shadow) !important;
    height: var(--nav-height) !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 1000 !important;
    backdrop-filter: blur(8px) !important;

    // Modern gradient accent stripe at bottom
    &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg,
            var(--nav-active-underline) 0%,
            #00E5FF 50%,
            var(--nav-active-underline) 100%);
        opacity: 0.6;
        animation: shimmer 3s ease-in-out infinite;
    }

    // Responsive height adjustments with proper spacing
    @media (max-width: 992px) {
        height: var(--nav-height-tablet) !important;

        :root {
            --nav-font-size: var(--nav-font-size-tablet);
            --nav-item-padding-x: 12px;
            --nav-item-gap: 10px;
        }
    }

    @media (max-width: 640px) {
        height: var(--nav-height-mobile) !important;

        :root {
            --nav-font-size: var(--nav-font-size-mobile);
            --nav-item-padding-x: 10px;
            --nav-item-gap: 8px;
        }
    }

    // Container styling with generous spacing
    .deriv-header__container {
        max-width: 1200px !important;
        margin: 0 auto !important;
        padding: 0 var(--nav-item-padding-x) !important;
        height: 100% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        gap: var(--nav-item-gap) !important;
    }

    // Navigation wrapper with proper spacing
    .deriv-header__wrapper {
        display: flex !important;
        align-items: center !important;
        gap: var(--nav-item-gap) !important;
        height: 100% !important;

        &--left {
            flex: 0 0 auto !important;
        }

        &--center {
            flex: 1 1 auto !important;
            display: flex !important;
            justify-content: center !important;
            gap: var(--nav-item-gap) !important;
        }

        &--right {
            flex: 0 0 auto !important;
            display: flex !important;
            align-items: center !important;
            gap: var(--nav-item-gap) !important;
        }
    }
    // Enhanced menu item styling with professional typography
    &__menu {
        color: var(--nav-fg) !important;
        text-decoration: none !important;
        padding: var(--nav-item-padding-y) var(--nav-item-padding-x) !important;
        border-radius: var(--nav-radius) !important;
        transition: all 180ms ease !important;
        font-weight: var(--nav-font-weight) !important;
        font-size: var(--nav-font-size) !important;
        letter-spacing: var(--nav-letter-spacing) !important;
        position: relative !important;
        display: flex !important;
        align-items: center !important;
        gap: var(--nav-icon-gap) !important;
        min-height: 44px !important; // Accessibility touch target

        &:hover {
            color: var(--nav-fg-hover) !important;
            background: var(--nav-hover-bg) !important;
            transform: translateY(-1px) !important;

            // Subtle scale effect on icon
            svg {
                transform: scale(1.1) !important;
            }
        }

        &:active {
            transform: translateY(0) !important;
            background: rgba(255, 255, 255, 0.08) !important;
        }

        &--active,
        &[aria-current="page"] {
            color: var(--nav-fg-hover) !important;
            background: var(--nav-active-bg) !important;

            &::after {
                content: '';
                position: absolute;
                bottom: -2px;
                left: 50%;
                transform: translateX(-50%);
                width: 24px;
                height: 2px;
                background: var(--nav-active-underline);
                border-radius: 1px;
                animation: slideIn 300ms cubic-bezier(0.4, 0, 0.2, 1);
            }

            // Active state icon emphasis
            svg {
                transform: scale(1.05) !important;
                filter: drop-shadow(0 0 2px var(--nav-active-underline)) !important;
            }
        }

        // Icon styling
        svg {
            width: var(--nav-icon-size) !important;
            height: var(--nav-icon-size) !important;
            fill: currentColor !important;
            transition: fill 180ms ease !important;
            flex-shrink: 0 !important;
        }

        svg > path,
        svg > g > path {
            fill: currentColor !important;
        }

        // Text styling
        .dc-text,
        span {
            color: currentColor !important;
            font-weight: inherit !important;
            font-size: inherit !important;
            letter-spacing: inherit !important;
        }
    }

    &__account-settings {
        padding-inline-end: 1.2rem;
        border-inline-end: 0.1rem solid var(--nav-border);
        color: var(--nav-fg-muted) !important;

        &:hover {
            color: var(--nav-fg-hover) !important;
        }
    }

    &__account-settings,
    .notifications__wrapper {
        svg > path,
        svg > g > path {
            fill: currentColor !important;
            transition: fill 180ms ease !important;
        }
    }

    &__icons {
        display: flex;
        align-items: center;
        gap: var(--nav-item-gap) !important;
        margin-left: auto;
        padding-right: var(--nav-item-padding-x) !important;
    }

    &__icon-button {
        display: flex;
        align-items: center;
        justify-content: center;
        background: transparent !important;
        border: none !important;
        cursor: pointer !important;
        padding: var(--nav-item-padding-y) !important;
        border-radius: var(--nav-radius) !important;
        transition: all 180ms ease !important;
        color: var(--nav-fg) !important;
        width: 44px !important; // Generous touch target
        height: 44px !important;
        min-width: 44px !important;
        min-height: 44px !important;

        &:hover {
            background: var(--nav-hover-bg) !important;
            color: var(--nav-fg-hover) !important;
            transform: translateY(-1px) !important;
        }

        &:active {
            background: rgba(255, 255, 255, 0.12) !important;
            transform: translateY(0) !important;
        }

        svg {
            fill: currentColor !important;
            transition: fill 180ms ease !important;
            width: var(--nav-icon-size) !important;
            height: var(--nav-icon-size) !important;
            flex-shrink: 0 !important;
        }
    }

    // Enhanced CTA button styling with professional typography
    .auth-login-button {
        background: transparent !important;
        color: var(--nav-fg) !important;
        border: 1px solid var(--nav-border) !important;
        border-radius: var(--nav-radius) !important;
        padding: var(--nav-item-padding-y) var(--nav-item-padding-x) !important;
        font-weight: var(--nav-font-weight) !important;
        font-size: var(--nav-font-size) !important;
        letter-spacing: var(--nav-letter-spacing) !important;
        transition: all 180ms ease !important;
        min-height: 44px !important;

        &:hover:not([disabled]) {
            background: var(--nav-hover-bg) !important;
            border-color: var(--nav-fg) !important;
            color: var(--nav-fg-hover) !important;
            transform: translateY(-1px) !important;
        }

        .dc-btn__text {
            color: currentColor !important;
            font-weight: inherit !important;
            font-size: inherit !important;
            letter-spacing: inherit !important;
        }
    }

    .auth-signup-button {
        background: var(--nav-cta-bg) !important;
        color: var(--nav-cta-fg) !important;
        border: 1px solid var(--nav-cta-bg) !important;
        border-radius: 999px !important; // Pill shape for CTA
        padding: var(--nav-item-padding-y) calc(var(--nav-item-padding-x) + 6px) !important;
        font-weight: var(--nav-font-weight-cta) !important;
        font-size: var(--nav-font-size) !important;
        letter-spacing: var(--nav-letter-spacing) !important;
        transition: all 180ms cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow: 0 2px 8px var(--nav-cta-glow) !important;
        min-height: 44px !important;
        position: relative !important;
        overflow: hidden !important;

        // Subtle gradient overlay for depth
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            border-radius: inherit;
            opacity: 0;
            transition: opacity 180ms ease;
        }

        &:hover:not([disabled]) {
            background: var(--nav-cta-hover) !important;
            border-color: var(--nav-cta-hover) !important;
            transform: translateY(-2px) scale(1.02) !important;
            box-shadow: 0 6px 20px var(--nav-cta-glow-hover) !important;

            &::before {
                opacity: 1;
            }
        }

        &:active:not([disabled]) {
            transform: translateY(-1px) scale(1.01) !important;
            box-shadow: 0 3px 12px var(--nav-cta-glow) !important;
        }

        .dc-btn__text {
            color: var(--nav-cta-fg) !important;
            font-weight: inherit !important;
            font-size: inherit !important;
            letter-spacing: inherit !important;
            position: relative !important;
            z-index: 1 !important;
        }
    }
}

.account-switcher {
    &__item {
        .deriv-account-switcher-item {
            padding: 0.6rem 1.6rem;

            &__detail {
                .deriv-account-switcher-item__currency {
                    margin-bottom: 2px;
                    font-size: 1.4rem;
                }
            }

            &__icon {
                margin-right: unset;
                margin-inline-end: 10px;
            }
        }

        &--disabled {
            opacity: 0.32;
        }
    }

    &__separator:after {
        background-color: var(--general-section-2);
        content: '';
        height: 4px;
        position: absolute;
        width: 100%;
        z-index: 1;
    }

    &-footer {
        background: var(--general-main-1);

        &__actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 1rem;
            background-color: var(--du-general-main-2);

            &--hide-manage-button {
                justify-content: flex-end;
            }

            .manage-button {
                background: none;
                border: 1px solid var(--button-secondary-default);
                margin-inline-end: 0.7rem;
                height: auto;
                padding: 0.4rem 1.2rem;
                white-space: break-spaces;

                &:hover {
                    background-color: unset !important;
                }

                span {
                    color: var(--text-prominent);
                    font-size: var(--text-size-xs);
                    cursor: pointer;
                    font-weight: 700;
                }
            }
        }
    }
}

.derivs-secondary-tabs {
    display: flex;
    justify-content: center;
    width: 100%;
    position: sticky;
    top: 0;
    z-index: 1;
    background: var(--general-main-1);

    .derivs-text__size--md {
        font-size: 14px;
        line-height: 18px;
    }
}

.deriv-account-switcher {
    &-item {
        &__balance {
            button {
                border-width: 1px;
                height: 2.4rem;
                min-width: 4.8rem;

                span {
                    color: var(--text-prominent) !important;
                }
            }
        }
    }

    &__button {
        margin-inline-end: 0.8rem;

        @include mobile-or-tablet-screen {
            margin-inline-end: 0;
        }

        .deriv-context-menu {
            box-shadow: 0 0 2px 0 var(--du-shadow-box, #********);
        }

        .deriv-account-switcher__balance + svg > path {
            fill: var(--text-general);
        }

        &:hover {
            background-color: unset;
        }
    }

    &__currency {
        margin-bottom: 2px;
    }

    &__icon {
        margin-right: 7px;
    }

    &__balance {
        padding: 0 1rem 0.1rem 0;
    }

    &__list {
        &.account-switcher-panel {
            padding: 0.4rem 0.8rem 0;

            .account-switcher-panel__no-eu-accounts {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .add-button {
                    background: none;

                    span {
                        color: #000;
                        font-size: 12px;
                        border: solid 2px var(--text-disabled-1);
                        padding: 0.1rem 1.6rem;
                        border-radius: 0.5em;
                        cursor: pointer;
                    }
                }

                .deriv-account-switcher-item {
                    background: none;
                }
            }
        }

        .deriv-accordion {
            &__content {
                background: unset;
                overflow: auto;
            }

            &__text {
                margin-top: 0;
            }

            &__icon {
                margin-left: unset;
                margin-inline-start: auto;
            }
        }
    }

    &__title {
        height: 4rem;
        margin: 0 auto;
        padding: 1.2rem 1.6rem;
    }

    &__container--mobile {
        min-height: auto;
        max-width: 600px;
    }

    &__container {
        max-height: 80vh;
        overflow: auto;
        background: var(--general-main-1);
        background-color: var(--general-main-2);
        border-radius: 4px;
        box-shadow: 0 8px 16px 0 var(--shadow-menu) !important;
        position: absolute;
        transition:
            transform 0.3s ease,
            opacity 0.25s linear;
        width: 320px;
        top: 55px;
        right: unset;
        inset-inline-end: 0;
    }

    &__logout {
        align-items: center;
        display: flex;
        grid-column: 2 / 3;
        justify-content: flex-end;
        justify-self: end;
        padding: 1.6em 1.3em;

        &-text {
            margin-inline-end: 1rem;
            font-size: var(--text-size-xs);
        }

        &--loader {
            display: flex;
            justify-content: flex-end;
            padding: 16px;
        }
    }

    &__tradershub-link {
        background: var(--du-general-main-2);
        color: var(--text-prominent);
        padding: 1rem;
        display: flex;
        justify-content: center;
    }

    &__footer {
        padding: 0;
        display: block;
    }
}

.languages-modal {
    background: var(--general-section-1);

    .deriv-modal__header {
        border-bottom: 2px solid var(--general-section-5);
    }

    .deriv-modal__close-icon {
        path {
            fill: var(--text-prominent);
        }
    }

    .deriv-text {
        color: var(--text-prominent) !important;
    }
}

.auth-actions {
    display: flex;
    padding-block: 8px;

    button {
        font-weight: 400;
        margin-inline-end: 1.6rem;
    }
}

// Professional animations and micro-interactions
@keyframes slideIn {
    from {
        width: 0;
        opacity: 0;
        transform: translateX(-50%) scaleX(0);
    }
    to {
        width: 24px;
        opacity: 1;
        transform: translateX(-50%) scaleX(1);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 2px 8px var(--nav-cta-glow);
    }
    50% {
        box-shadow: 0 4px 16px var(--nav-cta-glow-hover);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes slideUnderline {
    from {
        width: 0;
        left: 50%;
    }
    to {
        width: 100%;
        left: 0;
    }
}

// Comprehensive accessibility enhancements
@media (prefers-reduced-motion: reduce) {
    .app-header {
        // Disable all transforms and animations for users who prefer reduced motion
        &__menu,
        &__icon-button,
        .auth-login-button,
        .auth-signup-button {
            transition: color 180ms ease, background-color 180ms ease !important;
            transform: none !important;

            &:hover,
            &:focus,
            &:active {
                transform: none !important;
            }

            svg {
                transform: none !important;
            }
        }

        &__menu--active::after {
            animation: none !important;
            width: 24px !important;
            opacity: 1 !important;
        }

        // Disable gradient animation
        &::after {
            animation: none !important;
        }
    }

    .mobile-menu {
        &__content__items__item {
            &:hover {
                transform: none !important;
            }
        }
    }
}

// High contrast mode support
@media (prefers-contrast: high) {
    .app-header {
        --nav-bg: #000000 !important;
        --nav-border: #FFFFFF !important;
        --nav-fg: #FFFFFF !important;
        --nav-fg-hover: #FFFFFF !important;
        --nav-active-bg: #FFFFFF !important;
        --nav-active-underline: #FFFFFF !important;
        --nav-hover-bg: rgba(255, 255, 255, 0.2) !important;

        border-bottom: 2px solid #FFFFFF !important;

        &__menu--active {
            color: #000000 !important;

            &::after {
                background: #000000 !important;
                height: 3px !important;
            }
        }

        .auth-signup-button {
            --nav-cta-bg: #FFFFFF !important;
            --nav-cta-fg: #000000 !important;
            border: 2px solid #FFFFFF !important;
        }
    }
}

// Screen reader and keyboard navigation support
.app-header {
    // Skip link for keyboard users
    &__skip-link {
        position: absolute;
        top: -40px;
        left: 6px;
        background: var(--nav-cta-bg);
        color: var(--nav-cta-fg);
        padding: 8px 16px;
        border-radius: var(--nav-radius);
        text-decoration: none;
        font-weight: 600;
        z-index: 1001;
        transition: top 180ms ease;

        &:focus {
            top: 6px;
        }
    }

    // Ensure proper focus order and visibility
    &__menu,
    &__icon-button,
    .auth-login-button,
    .auth-signup-button {
        // Ensure elements are focusable
        &:focus {
            z-index: 10;
        }

        // Screen reader text for icon-only buttons
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }
    }

    // Ensure sufficient color contrast
    &__menu {
        // Active state must have sufficient contrast
        &--active,
        &[aria-current="page"] {
            // Ensure 4.5:1 contrast ratio
            color: #FFFFFF !important;

            &::after {
                // Ensure underline is visible
                background: var(--nav-active-underline) !important;
                box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1) !important;
            }
        }
    }
}

// Enhanced responsive design with optimized spacing
@media (max-width: 1200px) {
    .app-header {
        .deriv-header__container {
            padding: 0 20px !important;
        }
    }
}

@media (max-width: 992px) {
    .app-header {
        height: var(--nav-height-tablet) !important;

        &__menu {
            padding: 8px 12px !important;
            font-size: var(--nav-font-size-tablet) !important;
            min-height: 40px !important;
            gap: 6px !important;
        }

        &__icon-button {
            width: 40px !important;
            height: 40px !important;
            padding: 8px !important;
            min-width: 40px !important;
            min-height: 40px !important;

            svg {
                width: 16px !important;
                height: 16px !important;
            }
        }

        &__icons {
            gap: 10px !important;
            padding-right: 12px !important;
        }

        .auth-login-button,
        .auth-signup-button {
            padding: 8px 14px !important;
            font-size: var(--nav-font-size-tablet) !important;
            min-height: 40px !important;
        }

        .deriv-header__container {
            padding: 0 16px !important;
        }
    }
}

@media (max-width: 768px) {
    .app-header {
        &__menu {
            // Hide some menu items on tablet, show in mobile drawer
            &:nth-child(n+4) {
                display: none !important;
            }
        }

        .auth-login-button {
            display: flex !important; // Show login button on tablets
            padding: 6px 12px !important;
            font-size: 13px !important;
            min-height: 36px !important;
        }
    }
}

@media (max-width: 640px) {
    .app-header {
        height: var(--nav-height-mobile) !important;

        &__menu {
            display: none !important; // Hide all menu items on mobile
        }

        &__icon-button {
            width: 44px !important; // Larger touch targets on mobile
            height: 44px !important;
            padding: 10px !important;
            min-width: 44px !important;
            min-height: 44px !important;

            svg {
                width: 18px !important;
                height: 18px !important;
            }
        }

        &__icons {
            gap: 8px !important;
            padding-right: 8px !important;
        }

        .auth-login-button {
            display: flex !important; // Show login button on mobile
            padding: 6px 10px !important;
            font-size: 12px !important;
            min-height: 36px !important;
            border-radius: 18px !important;
        }

        .auth-signup-button {
            padding: 8px 16px !important;
            font-size: var(--nav-font-size-mobile) !important;
            min-height: 44px !important;
            border-radius: 22px !important; // More rounded on mobile
        }

        .deriv-header__container {
            padding: 0 12px !important;
        }
    }
}

@media (max-width: 480px) {
    .app-header {
        .auth-signup-button {
            padding: 6px 12px !important;
            font-size: 12px !important;
            min-height: 40px !important;
        }

        &__icons {
            gap: 6px !important;
        }

        .deriv-header__container {
            padding: 0 8px !important;
        }
    }
}

// Enhanced focus states for accessibility with visual appeal
.app-header {
    &__menu:focus,
    &__menu:focus-visible {
        outline: 2px solid var(--nav-active-underline) !important;
        outline-offset: 2px !important;
        box-shadow: 0 0 0 4px rgba(255, 213, 79, 0.2) !important;
        background: var(--nav-active-bg) !important;
        color: var(--nav-fg-hover) !important;
        transform: translateY(-1px) !important;
    }

    &__icon-button:focus,
    &__icon-button:focus-visible {
        outline: 2px solid var(--nav-active-underline) !important;
        outline-offset: 2px !important;
        box-shadow: 0 0 0 4px rgba(255, 213, 79, 0.2) !important;
        background: var(--nav-hover-bg) !important;
        color: var(--nav-fg-hover) !important;
        transform: translateY(-1px) !important;
    }

    .auth-login-button:focus,
    .auth-login-button:focus-visible {
        outline: 2px solid var(--nav-active-underline) !important;
        outline-offset: 2px !important;
        box-shadow: 0 0 0 4px rgba(255, 213, 79, 0.2) !important;
        background: var(--nav-hover-bg) !important;
        color: var(--nav-fg-hover) !important;
        transform: translateY(-1px) !important;
    }

    .auth-signup-button:focus,
    .auth-signup-button:focus-visible {
        outline: 2px solid var(--nav-active-underline) !important;
        outline-offset: 2px !important;
        box-shadow: 0 0 0 4px rgba(255, 213, 79, 0.2), 0 6px 20px var(--nav-cta-glow-hover) !important;
        transform: translateY(-2px) scale(1.02) !important;

        &::before {
            opacity: 1 !important;
        }
    }

    // Remove default focus styles when focus-visible is supported
    &__menu:focus:not(:focus-visible),
    &__icon-button:focus:not(:focus-visible),
    .auth-login-button:focus:not(:focus-visible),
    .auth-signup-button:focus:not(:focus-visible) {
        outline: none !important;
        box-shadow: none !important;
    }
}

.deposit-button,
.manage-funds-button {
    margin-inline-end: 1.6rem;
}

.deriv-accordion--underline {
    border-bottom: none;
}
