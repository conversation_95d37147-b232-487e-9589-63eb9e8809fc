// Enhanced menu items styling for Clean "Pro" dark bar with professional typography
.app-header__menu {
    gap: var(--nav-icon-gap, 8px) !important;
    padding: var(--nav-item-padding-y, 10px) var(--nav-item-padding-x, 14px) !important;
    color: var(--nav-fg, rgba(255, 255, 255, 0.85)) !important;
    text-decoration: none !important;
    border-radius: var(--nav-radius, 10px) !important;
    transition: all 180ms ease !important;
    font-weight: var(--nav-font-weight, 500) !important;
    font-size: var(--nav-font-size, 14px) !important;
    letter-spacing: var(--nav-letter-spacing, 0.2px) !important;
    position: relative !important;
    display: flex !important;
    align-items: center !important;
    min-height: 44px !important; // Accessibility touch target

    &:hover {
        color: var(--nav-fg-hover, #FFFFFF) !important;
        background: var(--nav-hover-bg, rgba(255, 255, 255, 0.05)) !important;
        transform: translateY(-1px) !important;
    }

    &--active,
    &[aria-current="page"] {
        color: var(--nav-fg-hover, #FFFFFF) !important;
        background: var(--nav-active-bg, rgba(255, 213, 79, 0.12)) !important;

        &::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 24px;
            height: 2px;
            background: var(--nav-active-underline, #FFD54F);
            border-radius: 1px;
            animation: slideIn 200ms ease;
        }
    }

    // Icon styling within menu items
    svg {
        fill: currentColor !important;
        transition: fill 180ms ease !important;
        width: var(--nav-icon-size, 16px) !important;
        height: var(--nav-icon-size, 16px) !important;
        flex-shrink: 0 !important;
    }

    // Text styling
    .dc-text,
    span {
        color: currentColor !important;
        font-weight: inherit !important;
        font-size: inherit !important;
        letter-spacing: inherit !important;
    }
}

// Responsive spacing adjustments for menu items
@media (max-width: 992px) {
    .app-header__menu {
        padding: 8px 12px !important;
        font-size: var(--nav-font-size-tablet, 13px) !important;
        gap: 6px !important;
        min-height: 40px !important; // Slightly smaller on tablet

        svg {
            width: 14px !important;
            height: 14px !important;
        }
    }
}

@media (max-width: 640px) {
    .app-header__menu {
        padding: 6px 10px !important;
        font-size: var(--nav-font-size-mobile, 13px) !important;
        gap: 4px !important;
        min-height: 40px !important; // Maintain touch target on mobile

        svg {
            width: 14px !important;
            height: 14px !important;
        }
    }
}

// Animation keyframes
@keyframes slideIn {
    from {
        width: 0;
        opacity: 0;
    }
    to {
        width: 24px;
        opacity: 1;
    }
}

// Accessibility enhancements for menu items
.app-header__menu {
    // Screen reader only text
    .sr-only {
        position: absolute !important;
        width: 1px !important;
        height: 1px !important;
        padding: 0 !important;
        margin: -1px !important;
        overflow: hidden !important;
        clip: rect(0, 0, 0, 0) !important;
        white-space: nowrap !important;
        border: 0 !important;
    }

    // Enhanced focus states for keyboard navigation
    &:focus-visible {
        outline: 2px solid var(--nav-active-underline, #FFD54F) !important;
        outline-offset: 2px !important;
        box-shadow: 0 0 0 4px rgba(255, 213, 79, 0.2) !important;
        background: var(--nav-active-bg, rgba(255, 213, 79, 0.12)) !important;
        color: var(--nav-fg-hover, #FFFFFF) !important;
    }

    // Remove default focus when focus-visible is supported
    &:focus:not(:focus-visible) {
        outline: none !important;
        box-shadow: none !important;
    }

    // Ensure proper contrast in all states
    &:hover,
    &:focus,
    &--active,
    &[aria-current="page"] {
        // Ensure minimum 4.5:1 contrast ratio
        color: #FFFFFF !important;
    }
}

// High contrast mode support for menu items
@media (prefers-contrast: high) {
    .app-header__menu {
        border: 1px solid transparent !important;

        &:hover,
        &:focus,
        &--active,
        &[aria-current="page"] {
            border-color: #FFFFFF !important;
            background: #000000 !important;
            color: #FFFFFF !important;
        }

        &--active::after,
        &[aria-current="page"]::after {
            background: #FFFFFF !important;
            height: 3px !important;
            box-shadow: 0 0 0 1px #000000 !important;
        }
    }
}

// Reduced motion support for menu items
@media (prefers-reduced-motion: reduce) {
    .app-header__menu {
        transition: color 180ms ease, background-color 180ms ease !important;

        &:hover,
        &:focus {
            transform: none !important;
        }

        svg {
            transform: none !important;
        }

        &--active::after,
        &[aria-current="page"]::after {
            animation: none !important;
            width: 24px !important;
            opacity: 1 !important;
        }
    }
}
